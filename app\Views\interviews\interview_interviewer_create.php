<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-user-plus mr-2"></i>
                        Add Interviewer
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews') ?>">Interviews</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews/open/' . $position['id']) ?>">Open Interview</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews/interviewers/' . $positionId) ?>">Interviewers</a></li>
                        <li class="breadcrumb-item active">Add Interviewer</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Position Information Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-briefcase mr-2"></i>
                        Position: <?= esc($position['position_no']) ?> - <?= esc($position['designation']) ?>
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('interviews/interviewers/' . $positionId) ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Back to Interviewers
                        </a>
                    </div>
                </div>
            </div>

            <!-- Add Interviewer Form -->
            <div class="card card-success card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-plus mr-2"></i>
                        New Interviewer Details
                    </h3>
                </div>
                <form method="POST" action="<?= base_url('interviews/interviewers/store/' . $positionId) ?>">
                    <?= csrf_field() ?>
                    <div class="card-body">
                        <?php if (session()->getFlashdata('error')): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <?= session()->getFlashdata('error') ?>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="interviewer_name">Interviewer Name <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="interviewer_name" 
                                           name="interviewer_name" 
                                           placeholder="Enter full name of the interviewer"
                                           value="<?= old('interviewer_name') ?>" 
                                           required>
                                    <small class="form-text text-muted">
                                        Enter the full name of the person who will be conducting the interview.
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="interviewer_position">Position/Title <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="interviewer_position" 
                                           name="interviewer_position" 
                                           placeholder="Enter position or job title"
                                           value="<?= old('interviewer_position') ?>" 
                                           required>
                                    <small class="form-text text-muted">
                                        Enter the interviewer's current position or job title.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Interviewer Role Guidelines -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card card-light">
                                    <div class="card-header">
                                        <h6 class="card-title">
                                            <i class="fas fa-info-circle mr-1"></i>
                                            Interviewer Role Guidelines
                                        </h6>
                                        <div class="card-tools">
                                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <h6>Recommended Panel Composition:</h6>
                                                <ul class="list-unstyled">
                                                    <li><small><strong>• Lead Interviewer:</strong> Department Head or Senior Manager</small></li>
                                                    <li><small><strong>• Technical Evaluator:</strong> Subject Matter Expert</small></li>
                                                    <li><small><strong>• HR Representative:</strong> Human Resources Personnel</small></li>
                                                    <li><small><strong>• Peer Representative:</strong> Current Team Member (if applicable)</small></li>
                                                </ul>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Interviewer Responsibilities:</h6>
                                                <ul class="list-unstyled">
                                                    <li><small>• Review candidate applications and resumes</small></li>
                                                    <li><small>• Prepare relevant questions for their expertise area</small></li>
                                                    <li><small>• Evaluate candidates fairly and objectively</small></li>
                                                    <li><small>• Provide constructive feedback and scores</small></li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sample Positions for Quick Selection -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Quick Select Common Positions:</label>
                                    <div class="btn-group-toggle" data-toggle="buttons">
                                        <label class="btn btn-outline-secondary btn-sm position-btn" data-position="Department Head">
                                            Department Head
                                        </label>
                                        <label class="btn btn-outline-secondary btn-sm position-btn" data-position="Senior Manager">
                                            Senior Manager
                                        </label>
                                        <label class="btn btn-outline-secondary btn-sm position-btn" data-position="HR Manager">
                                            HR Manager
                                        </label>
                                        <label class="btn btn-outline-secondary btn-sm position-btn" data-position="Technical Lead">
                                            Technical Lead
                                        </label>
                                        <label class="btn btn-outline-secondary btn-sm position-btn" data-position="Team Leader">
                                            Team Leader
                                        </label>
                                        <label class="btn btn-outline-secondary btn-sm position-btn" data-position="Subject Matter Expert">
                                            Subject Matter Expert
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">
                                        Click on any position above to quickly fill the position field.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-user-plus mr-1"></i>
                                    Add Interviewer
                                </button>
                                <a href="<?= base_url('interviews/interviewers/' . $positionId) ?>" class="btn btn-secondary ml-2">
                                    <i class="fas fa-times mr-1"></i>
                                    Cancel
                                </a>
                            </div>
                            <div class="col-md-6 text-right">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    All fields marked with <span class="text-danger">*</span> are required
                                </small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Quick position selection
    $('.position-btn').on('click', function(e) {
        e.preventDefault();
        var position = $(this).data('position');
        $('#interviewer_position').val(position);
        
        // Visual feedback
        $('.position-btn').removeClass('active');
        $(this).addClass('active');
    });

    // Form validation
    $('form').on('submit', function(e) {
        var interviewerName = $('#interviewer_name').val().trim();
        var interviewerPosition = $('#interviewer_position').val().trim();

        if (interviewerName.length < 2) {
            e.preventDefault();
            alert('Interviewer name must be at least 2 characters long.');
            $('#interviewer_name').focus();
            return false;
        }

        if (interviewerPosition.length < 2) {
            e.preventDefault();
            alert('Position/title must be at least 2 characters long.');
            $('#interviewer_position').focus();
            return false;
        }
    });

    // Auto-capitalize names
    $('#interviewer_name').on('input', function() {
        var words = $(this).val().split(' ');
        for (var i = 0; i < words.length; i++) {
            if (words[i].length > 0) {
                words[i] = words[i][0].toUpperCase() + words[i].substr(1).toLowerCase();
            }
        }
        $(this).val(words.join(' '));
    });
});
</script>
<?= $this->endSection() ?>
