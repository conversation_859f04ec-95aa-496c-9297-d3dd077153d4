<?= $this->extend('templates/adminlte/admindash') ?>

<?= $this->section('content') ?>
<div class="content-wrapper">
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>
                        <i class="fas fa-user-edit mr-2"></i>
                        Edit Interviewer
                    </h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews') ?>">Interviews</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews/open/' . $position['id']) ?>">Open Interview</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('interviews/interviewers/' . $interviewer['position_id']) ?>">Interviewers</a></li>
                        <li class="breadcrumb-item active">Edit Interviewer</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <section class="content">
        <div class="container-fluid">
            <!-- Position Information Card -->
            <div class="card card-primary card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-briefcase mr-2"></i>
                        Position: <?= esc($position['position_no']) ?> - <?= esc($position['designation']) ?>
                    </h3>
                    <div class="card-tools">
                        <a href="<?= base_url('interviews/interviewers/' . $interviewer['position_id']) ?>" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left mr-1"></i>
                            Back to Interviewers
                        </a>
                    </div>
                </div>
            </div>

            <!-- Edit Interviewer Form -->
            <div class="card card-warning card-outline">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user-edit mr-2"></i>
                        Edit Interviewer Details
                    </h3>
                </div>
                <form method="POST" action="<?= base_url('interviews/interviewers/update/' . $interviewer['id']) ?>">
                    <?= csrf_field() ?>
                    <div class="card-body">
                        <?php if (session()->getFlashdata('error')): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <?= session()->getFlashdata('error') ?>
                            </div>
                        <?php endif; ?>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="interviewer_name">Interviewer Name <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="interviewer_name" 
                                           name="interviewer_name" 
                                           placeholder="Enter full name of the interviewer"
                                           value="<?= old('interviewer_name', $interviewer['interviewer_name']) ?>" 
                                           required>
                                    <small class="form-text text-muted">
                                        Enter the full name of the person who will be conducting the interview.
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="interviewer_position">Position/Title <span class="text-danger">*</span></label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="interviewer_position" 
                                           name="interviewer_position" 
                                           placeholder="Enter position or job title"
                                           value="<?= old('interviewer_position', $interviewer['interviewer_position']) ?>" 
                                           required>
                                    <small class="form-text text-muted">
                                        Enter the interviewer's current position or job title.
                                    </small>
                                </div>
                            </div>
                        </div>

                        <!-- Current Information Display -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="card card-light">
                                    <div class="card-header">
                                        <h6 class="card-title">
                                            <i class="fas fa-history mr-1"></i>
                                            Current Information
                                        </h6>
                                        <div class="card-tools">
                                            <button type="button" class="btn btn-tool" data-card-widget="collapse">
                                                <i class="fas fa-minus"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-4">
                                                <h6>Current Name:</h6>
                                                <div class="alert alert-light">
                                                    <?= esc($interviewer['interviewer_name']) ?>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <h6>Current Position:</h6>
                                                <div class="alert alert-light">
                                                    <?= esc($interviewer['interviewer_position']) ?>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <h6>Added On:</h6>
                                                <div class="alert alert-light">
                                                    <?= date('M d, Y \a\t h:i A', strtotime($interviewer['created_at'])) ?>
                                                    <?php if ($interviewer['updated_at'] && $interviewer['updated_at'] != $interviewer['created_at']): ?>
                                                        <br><small class="text-muted">Last updated: <?= date('M d, Y \a\t h:i A', strtotime($interviewer['updated_at'])) ?></small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Position Selection -->
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Quick Select Common Positions:</label>
                                    <div class="btn-group-toggle" data-toggle="buttons">
                                        <label class="btn btn-outline-secondary btn-sm position-btn" data-position="Department Head">
                                            Department Head
                                        </label>
                                        <label class="btn btn-outline-secondary btn-sm position-btn" data-position="Senior Manager">
                                            Senior Manager
                                        </label>
                                        <label class="btn btn-outline-secondary btn-sm position-btn" data-position="HR Manager">
                                            HR Manager
                                        </label>
                                        <label class="btn btn-outline-secondary btn-sm position-btn" data-position="Technical Lead">
                                            Technical Lead
                                        </label>
                                        <label class="btn btn-outline-secondary btn-sm position-btn" data-position="Team Leader">
                                            Team Leader
                                        </label>
                                        <label class="btn btn-outline-secondary btn-sm position-btn" data-position="Subject Matter Expert">
                                            Subject Matter Expert
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">
                                        Click on any position above to quickly update the position field.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <div class="row">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save mr-1"></i>
                                    Update Interviewer
                                </button>
                                <a href="<?= base_url('interviews/interviewers/' . $interviewer['position_id']) ?>" class="btn btn-secondary ml-2">
                                    <i class="fas fa-times mr-1"></i>
                                    Cancel
                                </a>
                            </div>
                            <div class="col-md-6 text-right">
                                <small class="text-muted">
                                    <i class="fas fa-info-circle mr-1"></i>
                                    All fields marked with <span class="text-danger">*</span> are required
                                </small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </section>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Quick position selection
    $('.position-btn').on('click', function(e) {
        e.preventDefault();
        var position = $(this).data('position');
        $('#interviewer_position').val(position);
        
        // Visual feedback
        $('.position-btn').removeClass('active');
        $(this).addClass('active');
    });

    // Form validation
    $('form').on('submit', function(e) {
        var interviewerName = $('#interviewer_name').val().trim();
        var interviewerPosition = $('#interviewer_position').val().trim();

        if (interviewerName.length < 2) {
            e.preventDefault();
            alert('Interviewer name must be at least 2 characters long.');
            $('#interviewer_name').focus();
            return false;
        }

        if (interviewerPosition.length < 2) {
            e.preventDefault();
            alert('Position/title must be at least 2 characters long.');
            $('#interviewer_position').focus();
            return false;
        }

        // Confirm update
        if (!confirm('Are you sure you want to update this interviewer information?')) {
            e.preventDefault();
            return false;
        }
    });

    // Auto-capitalize names
    $('#interviewer_name').on('input', function() {
        var words = $(this).val().split(' ');
        for (var i = 0; i < words.length; i++) {
            if (words[i].length > 0) {
                words[i] = words[i][0].toUpperCase() + words[i].substr(1).toLowerCase();
            }
        }
        $(this).val(words.join(' '));
    });
});
</script>
<?= $this->endSection() ?>
